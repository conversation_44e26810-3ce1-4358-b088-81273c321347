import { ApiResponse } from "@/types/apiResonse";
import { ErrorResponse } from "@/types/errorResponse";
import { SpecialtyCreationRequest, SpecialtyCreationResponse, SpecialtyDetailResponse } from "@/types/specialty";
import { PageResponse } from "@/types/pageResponse";
import { API_URL } from "@/utils/baseUrl";
import { fetchInterceptor } from "@/utils/interceptor";

export const createSpecialty = async (data: SpecialtyCreationRequest): Promise<ApiResponse<SpecialtyCreationResponse>> => {
    const response = await fetchInterceptor(`${API_URL}/api/v1/specialty`, {
        method: "POST",
        body: JSON.stringify(data)
    });

    const result: ApiResponse<SpecialtyCreationResponse> = await response.json();
    return result;
};

export const getSpecialties = async (page: number = 1, size: number = 10, keyword: string = ""): Promise<ApiResponse<PageResponse<SpecialtyDetailResponse>>> => {
    let url = `${API_URL}/api/v1/specialty?page=${page}&size=${size}`;
    if (keyword.trim()) {
        url += `&keyword=${encodeURIComponent(keyword)}`;
    }

    const response = await fetchInterceptor(url, {
        method: "GET"
    });

    const result: ApiResponse<PageResponse<SpecialtyDetailResponse>> = await response.json();
    return result;
};

export const deleteSpecialty = async (id: number): Promise<ApiResponse<object>> => {
    const response = await fetchInterceptor(`${API_URL}/api/v1/specialty/${id}`, {
        method: "DELETE"
    });

    const result: ApiResponse<object> = await response.json();
    return result;
};
